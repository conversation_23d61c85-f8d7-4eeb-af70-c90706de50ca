/**
 * 设置数据库集合脚本
 * 用于在运行测试前创建必要的集合
 *
 * 运行方式: npx ts-node packages/service/customer_activity/setup_collection.ts
 */

import { MongoClient } from 'mongodb'

async function setupCollections() {
  console.log('🔧 开始设置数据库集合...')

  let client: MongoClient | null = null

  try {
    // 直接使用MongoDB客户端连接
    const mongoUrl = process.env.DATABASE_URL || 'mongodb://localhost:27017/yuhe'
    client = new MongoClient(mongoUrl)

    console.log('📡 连接到MongoDB...')
    await client.connect()

    const db = client.db()

    // 检查集合是否存在
    const collections = await db.listCollections({ name: 'customer_activity' }).toArray()

    if (collections.length > 0) {
      console.log('✅ customer_activity 集合已存在')
    } else {
      // 创建集合
      console.log('📝 创建 customer_activity 集合...')
      await db.createCollection('customer_activity')
      console.log('✅ customer_activity 集合创建成功')
    }

    // 创建索引
    console.log('📋 创建索引...')
    const collection = db.collection('customer_activity')

    // 创建 chat_id 的唯一索引
    await collection.createIndex({ chat_id: 1 }, { unique: true })
    console.log('✅ chat_id 唯一索引创建成功')

    // 创建 updated_at 索引
    await collection.createIndex({ updated_at: 1 })
    console.log('✅ updated_at 索引创建成功')

    console.log('✅ 集合设置完成！')

  } catch (error: any) {
    console.error('❌ 设置集合失败:', error)

    // 如果是因为索引已存在而失败，这实际上是好事
    if (error.message && (error.message.includes('already exists') || error.code === 85)) {
      console.log('✅ 集合或索引已存在，无需创建')
    } else {
      throw error
    }
  } finally {
    if (client) {
      await client.close()
      console.log('🔌 MongoDB连接已关闭')
    }
  }
}

// 运行设置
if (require.main === module) {
  setupCollections().catch(console.error)
}

export { setupCollections }
