import { TimeSlot } from 'model/typegoose/models/customer_activity'

/**
 * 时段工具类
 */
export class TimeSlotUtils {
  /**
   * 根据小时数获取对应的时段
   * @param hour 小时数 (0-23)
   * @returns TimeSlot 或 null（如果不在8-24范围内）
   */
  public static getTimeSlotByHour(hour: number): TimeSlot | null {
    if (hour < 8 || hour >= 24) {
      return null
    }

    if (hour >= 8 && hour < 10) return TimeSlot.SLOT_08_10
    if (hour >= 10 && hour < 12) return TimeSlot.SLOT_10_12
    if (hour >= 12 && hour < 14) return TimeSlot.SLOT_12_14
    if (hour >= 14 && hour < 16) return TimeSlot.SLOT_14_16
    if (hour >= 16 && hour < 18) return TimeSlot.SLOT_16_18
    if (hour >= 18 && hour < 20) return TimeSlot.SLOT_18_20
    if (hour >= 20 && hour < 22) return TimeSlot.SLOT_20_22
    if (hour >= 22 && hour < 24) return TimeSlot.SLOT_22_24

    return null
  }

  /**
   * 根据Date对象获取对应的时段
   * @param date Date对象
   * @returns TimeSlot 或 null
   */
  public static getTimeSlotByDate(date: Date): TimeSlot | null {
    return this.getTimeSlotByHour(date.getHours())
  }

  /**
   * 获取当前时段
   * @returns TimeSlot 或 null
   */
  public static getCurrentTimeSlot(): TimeSlot | null {
    return this.getTimeSlotByDate(new Date())
  }

  /**
   * 获取所有时段
   * @returns TimeSlot[]
   */
  public static getAllTimeSlots(): TimeSlot[] {
    return Object.values(TimeSlot)
  }

  /**
   * 获取时段的显示名称
   * @param slot TimeSlot
   * @returns string
   */
  public static getTimeSlotDisplayName(slot: TimeSlot): string {
    const slotMap: Record<TimeSlot, string> = {
      [TimeSlot.SLOT_08_10]: '早上8-10点',
      [TimeSlot.SLOT_10_12]: '上午10-12点',
      [TimeSlot.SLOT_12_14]: '中午12-14点',
      [TimeSlot.SLOT_14_16]: '下午14-16点',
      [TimeSlot.SLOT_16_18]: '下午16-18点',
      [TimeSlot.SLOT_18_20]: '晚上18-20点',
      [TimeSlot.SLOT_20_22]: '晚上20-22点',
      [TimeSlot.SLOT_22_24]: '深夜22-24点'
    }
    return slotMap[slot]
  }

  /**
   * 获取时段的开始和结束小时
   * @param slot TimeSlot
   * @returns { start: number, end: number }
   */
  public static getTimeSlotHours(slot: TimeSlot): { start: number, end: number } {
    const hoursMap: Record<TimeSlot, { start: number, end: number }> = {
      [TimeSlot.SLOT_08_10]: { start: 8, end: 10 },
      [TimeSlot.SLOT_10_12]: { start: 10, end: 12 },
      [TimeSlot.SLOT_12_14]: { start: 12, end: 14 },
      [TimeSlot.SLOT_14_16]: { start: 14, end: 16 },
      [TimeSlot.SLOT_16_18]: { start: 16, end: 18 },
      [TimeSlot.SLOT_18_20]: { start: 18, end: 20 },
      [TimeSlot.SLOT_20_22]: { start: 20, end: 22 },
      [TimeSlot.SLOT_22_24]: { start: 22, end: 24 }
    }
    return hoursMap[slot]
  }

  /**
   * 判断指定时间是否在某个时段内
   * @param date Date对象
   * @param slot TimeSlot
   * @returns boolean
   */
  public static isTimeInSlot(date: Date, slot: TimeSlot): boolean {
    const hour = date.getHours()
    const { start, end } = this.getTimeSlotHours(slot)
    return hour >= start && hour < end
  }
}
