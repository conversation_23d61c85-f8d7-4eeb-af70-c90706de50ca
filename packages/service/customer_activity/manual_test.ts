/**
 * 手动测试脚本
 * 用于手动测试客户活跃时段功能
 * 
 * 运行方式: npx ts-node packages/service/customer_activity/manual_test.ts
 */

import { ActivityTracker } from './activity_tracker'
import { ActivityTimeManager } from './activity_time_manager'
import { TimeSlotUtils } from './time_slot_utils'
import { TimeSlot } from 'model/typegoose/models/customer_activity'
import { getMongoConnection, closeMongoConnections } from 'model/typegoose/connection/mongo_connection'

async function runManualTest() {
  console.log('🚀 开始手动测试客户活跃时段功能...')
  
  try {
    // 建立数据库连接
    console.log('📡 连接数据库...')
    await getMongoConnection('test_customer_activity')
    console.log('✅ 数据库连接成功')

    const testChatId = 'manual_test_chat_001'
    const customerProfile = `基本信息::店面类别: 实体店
基本信息::行业类目: 服装零售
基本信息::年营业额: 50万
抖音运营状态::是否抖音在做: 是`

    // 测试1: 初始化客户活跃时段数据
    console.log('\n📋 测试1: 初始化客户活跃时段数据')
    await ActivityTimeManager.initializeCustomerActivity(testChatId, customerProfile)
    console.log('✅ 客户活跃时段数据初始化完成')

    // 测试2: 获取初始统计信息
    console.log('\n📊 测试2: 获取初始统计信息')
    const initialStats = await ActivityTracker.getActivityStats(testChatId)
    console.log('初始统计信息:', JSON.stringify(initialStats, null, 2))

    // 测试3: 获取最佳活跃时段
    console.log('\n🎯 测试3: 获取最佳活跃时段')
    const bestSlots = await ActivityTimeManager.getBestActiveTimeSlots(testChatId, 3)
    console.log('最佳活跃时段:')
    bestSlots.forEach((slot, index) => {
      const displayName = TimeSlotUtils.getTimeSlotDisplayName(slot.slot)
      console.log(`  ${index + 1}. ${displayName} - 权重: ${slot.weight.toFixed(2)} ${slot.is_predicted ? '(AI预测)' : ''}`)
    })

    // 测试4: 模拟客户在不同时段发送消息
    console.log('\n💬 测试4: 模拟客户消息')
    const messageTimes = [
      { time: new Date('2024-01-01T08:30:00'), desc: '早上8:30' },
      { time: new Date('2024-01-01T12:30:00'), desc: '中午12:30' },
      { time: new Date('2024-01-01T18:30:00'), desc: '晚上18:30' },
      { time: new Date('2024-01-01T20:30:00'), desc: '晚上20:30' },
    ]

    for (const { time, desc } of messageTimes) {
      console.log(`  处理 ${desc} 的消息...`)
      await ActivityTracker.handleCustomerMessage(testChatId, time)
      
      const timeSlot = TimeSlotUtils.getTimeSlotByDate(time)
      if (timeSlot) {
        const displayName = TimeSlotUtils.getTimeSlotDisplayName(timeSlot)
        console.log(`    ✅ ${displayName} 权重已增加`)
      }
    }

    // 测试5: 查看权重变化
    console.log('\n📈 测试5: 查看权重变化')
    const updatedSlots = await ActivityTimeManager.getBestActiveTimeSlots(testChatId, 5)
    console.log('更新后的活跃时段:')
    updatedSlots.forEach((slot, index) => {
      const displayName = TimeSlotUtils.getTimeSlotDisplayName(slot.slot)
      console.log(`  ${index + 1}. ${displayName} - 权重: ${slot.weight.toFixed(2)} ${slot.is_predicted ? '(AI预测)' : ''}`)
    })

    // 测试6: 模拟未回复情况
    console.log('\n❌ 测试6: 模拟未回复情况')
    const noReplyTime = new Date('2024-01-01T14:30:00')
    console.log(`  模拟在 下午14:30 发送消息但客户未回复...`)
    await ActivityTracker.handleNoReply(testChatId, noReplyTime)
    
    const timeSlot = TimeSlotUtils.getTimeSlotByDate(noReplyTime)
    if (timeSlot) {
      const displayName = TimeSlotUtils.getTimeSlotDisplayName(timeSlot)
      console.log(`    ✅ ${displayName} 权重已减少`)
    }

    // 测试7: 获取最佳联系时间
    console.log('\n⏰ 测试7: 获取最佳联系时间')
    const bestContactTime = await ActivityTracker.getNextBestContactTime(testChatId)
    if (bestContactTime) {
      console.log('最佳联系时间:')
      console.log(`  时段: ${bestContactTime.displayName}`)
      console.log(`  权重: ${bestContactTime.weight.toFixed(2)}`)
      console.log(`  下次时间: ${bestContactTime.nextOccurrence.toLocaleString('zh-CN')}`)
    }

    // 测试8: 获取最终统计信息
    console.log('\n📊 测试8: 获取最终统计信息')
    const finalStats = await ActivityTracker.getActivityStats(testChatId)
    console.log('最终统计信息:', JSON.stringify(finalStats, null, 2))

    // 测试9: 测试权重配置更新
    console.log('\n⚙️ 测试9: 更新权重配置')
    await ActivityTimeManager.updateWeightConfig(testChatId, {
      increaseAmount: 0.3,
      decreaseAmount: 0.05,
      minWeight: 0.2,
      maxWeight: 1.0
    })
    console.log('✅ 权重配置更新完成')

    // 测试10: 测试边界情况
    console.log('\n🔍 测试10: 测试边界情况')
    
    // 测试非活跃时段
    const nonActiveTime = new Date('2024-01-01T03:30:00')
    console.log(`  测试非活跃时段 (凌晨3:30)...`)
    await ActivityTracker.handleCustomerMessage(testChatId, nonActiveTime)
    console.log('    ✅ 非活跃时段消息被正确忽略')

    // 测试不存在的客户
    console.log(`  测试不存在的客户...`)
    const nonExistentStats = await ActivityTracker.getActivityStats('non_existent_chat')
    console.log(`    ✅ 不存在的客户返回: ${nonExistentStats}`)

    console.log('\n🎉 所有测试完成！')

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  } finally {
    // 关闭数据库连接
    console.log('\n🔌 关闭数据库连接...')
    await closeMongoConnections()
    console.log('✅ 数据库连接已关闭')
  }
}

// 运行测试
if (require.main === module) {
  runManualTest().catch(console.error)
}

export { runManualTest }
