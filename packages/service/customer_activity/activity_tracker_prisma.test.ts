/**
 * ActivityTracker Prisma版本测试
 * 
 * 运行测试: npm test activity_tracker_prisma.test.ts
 */

import { ActivityTracker } from './activity_tracker'
import { ActivityTimeManager } from './activity_time_manager'
import { TimeSlotUtils } from './time_slot_utils'
import { TimeSlot, CustomerActivity } from 'model/prisma/models/customer_activity'
import { PrismaMongoClient } from 'model/mongodb/prisma'

describe('ActivityTracker Prisma Tests', () => {
  let prismaClient: any
  const testChatId = 'test_chat_activity_tracker_prisma'
  const testCustomerProfile = `基本信息::店面类别: 实体店
基本信息::行业类目: 服装零售
基本信息::年营业额: 50万
抖音运营状态::是否抖音在做: 是`

  beforeAll(async () => {
    // 建立数据库连接
    prismaClient = PrismaMongoClient.getInstance()
    console.log('Prisma客户端已初始化')
  }, 30000)

  afterAll(async () => {
    // 清理测试数据
    try {
      await prismaClient.customer_activity.deleteMany({
        where: {
          chat_id: {
            startsWith: 'test_'
          }
        }
      })
      console.log('测试数据已清理')
    } catch (error) {
      console.warn('清理测试数据失败:', error)
    }

    // 关闭数据库连接
    await prismaClient.$disconnect()
    console.log('Prisma连接已关闭')
  }, 10000)

  beforeEach(async () => {
    // 每个测试前清理特定的测试数据
    await prismaClient.customer_activity.deleteMany({
      where: { chat_id: testChatId }
    })
  })

  describe('handleCustomerMessage', () => {
    test('应该能够处理客户消息并增加当前时段权重', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)
      
      // 模拟在18:30发送消息（晚上18-20点时段）
      const messageTime = new Date('2024-01-01T18:30:00')
      
      // 获取初始权重
      const initialActivity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      const initialSlot = initialActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const initialWeight = initialSlot.weight
      
      // 处理客户消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      
      // 验证权重是否增加
      const updatedActivity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      const updatedSlot = updatedActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      
      expect(updatedSlot.weight).toBeGreaterThan(initialWeight)
      expect(updatedSlot.last_updated).toBeInstanceOf(Date)
      
      console.log(`权重从 ${initialWeight} 增加到 ${updatedSlot.weight}`)
    }, 15000)

    test('应该忽略非活跃时段的消息', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)
      
      // 模拟在凌晨2:30发送消息（非活跃时段）
      const messageTime = new Date('2024-01-01T02:30:00')
      
      // 获取初始状态
      const initialActivity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      const initialUpdateTime = initialActivity.updated_at
      
      // 处理客户消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      
      // 验证没有变化
      const updatedActivity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      expect(updatedActivity.updated_at.getTime()).toBe(initialUpdateTime.getTime())
      
      console.log('非活跃时段消息被正确忽略')
    }, 15000)
  })

  describe('handleNoReply', () => {
    test('应该能够处理未回复情况并减少时段权重', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)
      
      // 模拟在12:30发送消息但未回复（中午12-14点时段）
      const sentTime = new Date('2024-01-01T12:30:00')
      
      // 获取初始权重
      const initialActivity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      const initialSlot = initialActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_12_14)
      const initialWeight = initialSlot.weight
      
      // 处理未回复
      await ActivityTracker.handleNoReply(testChatId, sentTime)
      
      // 验证权重是否减少
      const updatedActivity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      const updatedSlot = updatedActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_12_14)
      
      expect(updatedSlot.weight).toBeLessThan(initialWeight)
      expect(updatedSlot.last_updated).toBeInstanceOf(Date)
      
      console.log(`权重从 ${initialWeight} 减少到 ${updatedSlot.weight}`)
    }, 15000)
  })

  describe('ensureCustomerActivityExists', () => {
    test('应该为新客户初始化活跃时段数据', async () => {
      const newChatId = 'test_new_customer_prisma'
      
      // 确保客户不存在
      await prismaClient.customer_activity.deleteMany({
        where: { chat_id: newChatId }
      })
      
      // 调用确保存在方法
      await ActivityTracker.ensureCustomerActivityExists(newChatId, testCustomerProfile)
      
      // 验证数据已创建
      const activity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: newChatId } 
      })
      expect(activity).toBeTruthy()
      expect(activity.time_slots).toHaveLength(8)
      
      // 清理
      await prismaClient.customer_activity.deleteMany({
        where: { chat_id: newChatId }
      })
      
      console.log('新客户活跃时段数据初始化成功')
    }, 15000)
  })

  describe('getActivityStats', () => {
    test('应该返回正确的统计信息', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)
      
      // 模拟一些活动来改变权重
      await ActivityTracker.handleCustomerMessage(testChatId, new Date('2024-01-01T18:30:00'))
      await ActivityTracker.handleCustomerMessage(testChatId, new Date('2024-01-01T20:30:00'))
      
      // 获取统计信息
      const stats = await ActivityTracker.getActivityStats(testChatId)
      
      expect(stats).toBeTruthy()
      expect(stats!.totalSlots).toBe(8)
      expect(stats!.predictedSlots).toBeGreaterThanOrEqual(0)
      expect(stats!.highWeightSlots).toBeGreaterThanOrEqual(0)
      expect(stats!.averageWeight).toBeGreaterThan(0)
      expect(stats!.lastUpdated).toBeInstanceOf(Date)
      
      console.log('活跃时段统计信息:', stats)
    }, 15000)
  })

  describe('冷却功能测试', () => {
    test('应该在冷却时间内阻止权重更新', async () => {
      // 初始化客户数据，设置短冷却时间便于测试
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        increaseAmount: 0.2,
        decreaseAmount: 0.1,
        minWeight: 0.1,
        maxWeight: 1.0,
        coolDown: 2000  // 2秒冷却时间
      })
      
      const messageTime = new Date('2024-01-01T18:30:00')
      
      // 第一次发送消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      
      // 获取第一次更新后的权重
      let activity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      let slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const firstWeight = slot.weight
      
      // 立即发送第二条消息（应该被冷却机制阻止）
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      
      // 检查权重是否没有变化
      activity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const secondWeight = slot.weight
      
      expect(secondWeight).toBe(firstWeight)
      console.log(`冷却机制生效，权重保持: ${secondWeight}`)
    }, 15000)

    test('应该在冷却时间结束后允许权重更新', async () => {
      // 初始化客户数据，设置很短的冷却时间
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        increaseAmount: 0.2,
        coolDown: 100  // 100毫秒冷却时间
      })
      
      const messageTime = new Date('2024-01-01T18:30:00')
      
      // 第一次发送消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      
      // 获取第一次更新后的权重
      let activity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      let slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const firstWeight = slot.weight
      
      // 等待冷却时间结束
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 再次发送消息（应该成功更新）
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      
      // 检查权重是否增加
      activity = await prismaClient.customer_activity.findUnique({ 
        where: { chat_id: testChatId } 
      })
      slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const secondWeight = slot.weight
      
      expect(secondWeight).toBeGreaterThan(firstWeight)
      console.log(`冷却时间结束，权重从 ${firstWeight} 增加到 ${secondWeight}`)
    }, 15000)
  })
})
