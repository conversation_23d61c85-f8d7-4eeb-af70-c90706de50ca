/**
 * 简单测试脚本
 * 用于验证Prisma版本的客户活跃时段功能
 * 
 * 运行方式: npx ts-node packages/service/customer_activity/simple_test.ts
 */

import { ActivityTracker } from './activity_tracker'
import { ActivityTimeManager } from './activity_time_manager'
import { TimeSlotUtils } from './time_slot_utils'
import { TimeSlot } from 'model/prisma/models/customer_activity'
import { PrismaMongoClient } from 'model/mongodb/prisma'
import { setupCollections } from './setup_collection'

async function runSimpleTest() {
  console.log('🧪 开始简单测试...')
  
  try {
    // 设置集合（跳过，让Prisma自动创建）
    console.log('📋 跳过集合设置，让Prisma自动创建...')
    
    const prismaClient = PrismaMongoClient.getInstance()
    const testChatId = 'simple_test_chat'
    const customerProfile = `基本信息::店面类别: 实体店
基本信息::行业类目: 服装零售
基本信息::年营业额: 50万
抖音运营状态::是否抖音在做: 是`

    // 清理之前的测试数据
    console.log('🧹 清理之前的测试数据...')
    await prismaClient.customer_activity.deleteMany({
      where: { chat_id: testChatId }
    })

    // 测试1: 初始化客户数据
    console.log('\n📋 测试1: 初始化客户数据')
    await ActivityTimeManager.initializeCustomerActivity(testChatId, customerProfile)
    console.log('✅ 客户数据初始化完成')

    // 验证数据是否创建成功
    const activity = await prismaClient.customer_activity.findUnique({
      where: { chat_id: testChatId }
    })
    
    if (activity) {
      console.log(`✅ 数据验证成功: 客户 ${testChatId} 有 ${activity.time_slots.length} 个时段`)
    } else {
      console.log('❌ 数据验证失败: 未找到客户数据')
      return
    }

    // 测试2: 处理客户消息
    console.log('\n💬 测试2: 处理客户消息')
    const messageTime = new Date('2024-01-01T18:30:00')
    
    // 获取初始权重
    const initialSlot = activity.time_slots.find(ts => ts.slot === TimeSlot.SLOT_18_20)
    const initialWeight = initialSlot?.weight || 0
    console.log(`初始权重: ${initialWeight}`)
    
    // 处理消息
    await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
    
    // 检查权重变化
    const updatedActivity = await prismaClient.customer_activity.findUnique({
      where: { chat_id: testChatId }
    })
    
    const updatedSlot = updatedActivity?.time_slots.find(ts => ts.slot === TimeSlot.SLOT_18_20)
    const updatedWeight = updatedSlot?.weight || 0
    
    if (updatedWeight > initialWeight) {
      console.log(`✅ 权重更新成功: ${initialWeight} -> ${updatedWeight}`)
    } else {
      console.log(`❌ 权重更新失败: ${initialWeight} -> ${updatedWeight}`)
    }

    // 测试3: 获取最佳时段
    console.log('\n🎯 测试3: 获取最佳时段')
    const bestSlots = await ActivityTimeManager.getBestActiveTimeSlots(testChatId, 3)
    
    if (bestSlots.length > 0) {
      console.log('✅ 最佳时段获取成功:')
      bestSlots.forEach((slot, index) => {
        const displayName = TimeSlotUtils.getTimeSlotDisplayName(slot.slot)
        console.log(`  ${index + 1}. ${displayName} - 权重: ${slot.weight.toFixed(2)}`)
      })
    } else {
      console.log('❌ 最佳时段获取失败')
    }

    // 测试4: 获取统计信息
    console.log('\n📊 测试4: 获取统计信息')
    const stats = await ActivityTracker.getActivityStats(testChatId)
    
    if (stats) {
      console.log('✅ 统计信息获取成功:')
      console.log(`  总时段数: ${stats.totalSlots}`)
      console.log(`  预测时段数: ${stats.predictedSlots}`)
      console.log(`  高权重时段数: ${stats.highWeightSlots}`)
      console.log(`  平均权重: ${stats.averageWeight}`)
    } else {
      console.log('❌ 统计信息获取失败')
    }

    // 测试5: 冷却机制
    console.log('\n⏰ 测试5: 冷却机制')
    
    // 立即再次发送消息（应该被冷却阻止）
    await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
    
    // 检查权重是否没有变化
    const cooldownActivity = await prismaClient.customer_activity.findUnique({
      where: { chat_id: testChatId }
    })
    
    const cooldownSlot = cooldownActivity?.time_slots.find(ts => ts.slot === TimeSlot.SLOT_18_20)
    const cooldownWeight = cooldownSlot?.weight || 0
    
    if (cooldownWeight === updatedWeight) {
      console.log('✅ 冷却机制工作正常，权重未变化')
    } else {
      console.log(`❌ 冷却机制失效，权重变化: ${updatedWeight} -> ${cooldownWeight}`)
    }

    // 清理测试数据
    console.log('\n🧹 清理测试数据...')
    await prismaClient.customer_activity.deleteMany({
      where: { chat_id: testChatId }
    })

    console.log('\n🎉 所有测试完成！')

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  } finally {
    // 关闭连接
    const prismaClient = PrismaMongoClient.getInstance()
    await prismaClient.$disconnect()
    console.log('🔌 数据库连接已关闭')
  }
}

// 运行测试
if (require.main === module) {
  runSimpleTest().catch(console.error)
}

export { runSimpleTest }
