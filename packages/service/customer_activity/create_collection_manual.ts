/**
 * 手动创建集合脚本
 * 使用原生MongoDB驱动创建customer_activity集合
 * 
 * 运行方式: npx ts-node packages/service/customer_activity/create_collection_manual.ts
 */

async function createCollectionManually() {
  console.log('🔧 手动创建customer_activity集合...')
  
  try {
    // 使用Prisma的原生查询功能来创建集合
    const { PrismaMongoClient } = await import('model/mongodb/prisma')
    const prismaClient = PrismaMongoClient.getInstance()
    
    // 使用Prisma的$runCommandRaw来执行MongoDB命令
    console.log('📝 执行MongoDB创建集合命令...')
    
    try {
      // 尝试创建集合
      await prismaClient.$runCommandRaw({
        create: 'customer_activity'
      })
      console.log('✅ customer_activity 集合创建成功')
    } catch (error: any) {
      if (error.message && error.message.includes('already exists')) {
        console.log('✅ customer_activity 集合已存在')
      } else {
        console.warn('⚠️ 创建集合时出现警告:', error.message)
      }
    }
    
    // 创建索引
    console.log('📋 创建索引...')
    
    try {
      // 创建chat_id的唯一索引
      await prismaClient.$runCommandRaw({
        createIndexes: 'customer_activity',
        indexes: [
          {
            key: { chat_id: 1 },
            name: 'chat_id_unique',
            unique: true
          }
        ]
      })
      console.log('✅ chat_id 唯一索引创建成功')
    } catch (error: any) {
      if (error.message && (error.message.includes('already exists') || error.code === 85)) {
        console.log('✅ chat_id 索引已存在')
      } else {
        console.warn('⚠️ 创建chat_id索引时出现警告:', error.message)
      }
    }
    
    try {
      // 创建updated_at索引
      await prismaClient.$runCommandRaw({
        createIndexes: 'customer_activity',
        indexes: [
          {
            key: { updated_at: 1 },
            name: 'updated_at_index'
          }
        ]
      })
      console.log('✅ updated_at 索引创建成功')
    } catch (error: any) {
      if (error.message && (error.message.includes('already exists') || error.code === 85)) {
        console.log('✅ updated_at 索引已存在')
      } else {
        console.warn('⚠️ 创建updated_at索引时出现警告:', error.message)
      }
    }
    
    console.log('🎉 集合和索引设置完成！')
    
    // 关闭连接
    await prismaClient.$disconnect()
    
  } catch (error: any) {
    console.error('❌ 创建集合失败:', error)
    throw error
  }
}

// 运行脚本
if (require.main === module) {
  createCollectionManually().catch(console.error)
}

export { createCollectionManually }
