import { ActivityTimeManager } from './activity_time_manager'
import { TimeSlotUtils } from './time_slot_utils'
import logger from 'model/logger/logger'

/**
 * 客户活跃时段跟踪器
 * 用于在消息处理流程中自动跟踪和更新客户活跃时段
 */
export class ActivityTracker {
  /**
   * 处理客户消息（增加当前时段权重）
   * @param chatId 聊天ID
   * @param messageTime 消息时间
   */
  public static async handleCustomerMessage(
    chatId: string,
    messageTime: Date = new Date()
  ): Promise<void> {
    try {
      // 检查是否在活跃时段范围内
      const timeSlot = TimeSlotUtils.getTimeSlotByDate(messageTime)
      if (!timeSlot) {
        logger.debug(`客户 ${chatId} 在非活跃时段 ${messageTime.getHours()}:${messageTime.getMinutes()} 发送消息`)
        return
      }

      // 增加当前时段权重
      await ActivityTimeManager.increaseCurrentSlotWeight(chatId, messageTime)

      logger.debug(`已处理客户 ${chatId} 在时段 ${timeSlot} 的消息，权重已更新`)
    } catch (error) {
      logger.error(`处理客户消息失败 for ${chatId}:`, error)
    }
  }

  /**
   * 处理客户未回复情况（减少指定时段权重）
   * @param chatId 聊天ID
   * @param sentTime 发送消息的时间
   */
  public static async handleNoReply(
    chatId: string,
    sentTime: Date
  ): Promise<void> {
    try {
      const timeSlot = TimeSlotUtils.getTimeSlotByDate(sentTime)
      if (!timeSlot) {
        logger.debug(`发送时间 ${sentTime.toISOString()} 不在活跃时段范围内`)
        return
      }

      // 减少该时段权重
      await ActivityTimeManager.decreaseSlotWeight(chatId, timeSlot)

      logger.debug(`客户 ${chatId} 在时段 ${timeSlot} 未回复，权重已降低`)
    } catch (error) {
      logger.error(`处理客户未回复失败 for ${chatId}:`, error)
    }
  }

  /**
   * 检查是否需要初始化客户活跃时段数据
   * @param chatId 聊天ID
   * @param customerProfile 客户画像
   */
  public static async ensureCustomerActivityExists(
    chatId: string,
    customerProfile?: string
  ): Promise<void> {
    try {
      const existing = await ActivityTimeManager.getCustomerActivity(chatId)
      if (!existing && customerProfile) {
        await ActivityTimeManager.initializeCustomerActivity(chatId, customerProfile)
        logger.log(`已为新客户 ${chatId} 初始化活跃时段数据`)
      }
    } catch (error) {
      logger.error(`确保客户活跃时段数据存在失败 for ${chatId}:`, error)
    }
  }

  /**
   * 获取客户下次主动触达的最佳时段
   * @param chatId 聊天ID
   * @returns 最佳时段信息，如果没有数据则返回null
   */
  public static async getNextBestContactTime(chatId: string): Promise<{
    slot: string
    weight: number
    displayName: string
    nextOccurrence: Date
  } | null> {
    try {
      const bestSlots = await ActivityTimeManager.getBestActiveTimeSlots(chatId, 1)
      if (bestSlots.length === 0) {
        return null
      }

      const bestSlot = bestSlots[0]
      const displayName = TimeSlotUtils.getTimeSlotDisplayName(bestSlot.slot)
      const nextOccurrence = this.calculateNextOccurrence(bestSlot.slot)

      return {
        slot: bestSlot.slot,
        weight: bestSlot.weight,
        displayName,
        nextOccurrence
      }
    } catch (error) {
      logger.error(`获取下次最佳联系时间失败 for ${chatId}:`, error)
      return null
    }
  }

  /**
   * 计算指定时段的下次出现时间
   * @param timeSlot 时段
   * @returns 下次出现的Date对象
   */
  private static calculateNextOccurrence(timeSlot: string): Date {
    const { start } = TimeSlotUtils.getTimeSlotHours(timeSlot as any)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), start, 0, 0)

    // 如果今天的时段已经过了，则返回明天的时段
    if (today <= now) {
      today.setDate(today.getDate() + 1)
    }

    return today
  }

  /**
   * 获取客户活跃时段统计信息
   * @param chatId 聊天ID
   * @returns 统计信息
   */
  public static async getActivityStats(chatId: string): Promise<{
    totalSlots: number
    predictedSlots: number
    highWeightSlots: number
    averageWeight: number
    lastUpdated: Date | null
  } | null> {
    try {
      const activity = await ActivityTimeManager.getCustomerActivity(chatId)
      if (!activity) {
        return null
      }

      const totalSlots = activity.time_slots.length
      const predictedSlots = activity.time_slots.filter((ts) => ts.is_predicted).length
      const highWeightSlots = activity.time_slots.filter((ts) => ts.weight >= 0.7).length
      const averageWeight = activity.time_slots.reduce((sum, ts) => sum + ts.weight, 0) / totalSlots
      const lastUpdated = activity.updated_at

      return {
        totalSlots,
        predictedSlots,
        highWeightSlots,
        averageWeight: Math.round(averageWeight * 100) / 100,
        lastUpdated
      }
    } catch (error) {
      logger.error(`获取活跃时段统计信息失败 for ${chatId}:`, error)
      return null
    }
  }
}
