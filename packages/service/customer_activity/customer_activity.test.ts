/**
 * 客户活跃时段功能测试
 */

import { TimeSlotUtils } from './time_slot_utils'
import { TimeSlot } from 'model/typegoose/models/customer_activity'
import { TimeSlotPredictor } from './time_slot_predictor'

describe('TimeSlotUtils', () => {
  describe('getTimeSlotByHour', () => {
    test('应该正确返回对应的时段', () => {
      expect(TimeSlotUtils.getTimeSlotByHour(8)).toBe(TimeSlot.SLOT_08_10)
      expect(TimeSlotUtils.getTimeSlotByHour(9)).toBe(TimeSlot.SLOT_08_10)
      expect(TimeSlotUtils.getTimeSlotByHour(10)).toBe(TimeSlot.SLOT_10_12)
      expect(TimeSlotUtils.getTimeSlotByHour(12)).toBe(TimeSlot.SLOT_12_14)
      expect(TimeSlotUtils.getTimeSlotByHour(18)).toBe(TimeSlot.SLOT_18_20)
      expect(TimeSlotUtils.getTimeSlotByHour(22)).toBe(TimeSlot.SLOT_22_24)
      expect(TimeSlotUtils.getTimeSlotByHour(23)).toBe(TimeSlot.SLOT_22_24)
    })

    test('应该对超出范围的小时返回null', () => {
      expect(TimeSlotUtils.getTimeSlotByHour(7)).toBeNull()
      expect(TimeSlotUtils.getTimeSlotByHour(24)).toBeNull()
      expect(TimeSlotUtils.getTimeSlotByHour(0)).toBeNull()
      expect(TimeSlotUtils.getTimeSlotByHour(25)).toBeNull()
    })
  })

  describe('getTimeSlotByDate', () => {
    test('应该正确根据Date对象返回时段', () => {
      const date1 = new Date('2024-01-01T08:30:00')
      expect(TimeSlotUtils.getTimeSlotByDate(date1)).toBe(TimeSlot.SLOT_08_10)

      const date2 = new Date('2024-01-01T14:15:00')
      expect(TimeSlotUtils.getTimeSlotByDate(date2)).toBe(TimeSlot.SLOT_14_16)

      const date3 = new Date('2024-01-01T21:45:00')
      expect(TimeSlotUtils.getTimeSlotByDate(date3)).toBe(TimeSlot.SLOT_20_22)
    })
  })
  describe('getCurrentTimeSlot', () => {
    test('应该正确返回当前时段', () => {
      const currentSlot = TimeSlotUtils.getCurrentTimeSlot()
      expect(currentSlot).not.toBeNull()
      expect(currentSlot).toContain(TimeSlot.SLOT_18_20)
    })
  })

  describe('getAllTimeSlots', () => {
    test('应该返回所有8个时段', () => {
      const allSlots = TimeSlotUtils.getAllTimeSlots()
      expect(allSlots).toHaveLength(8)
      expect(allSlots).toContain(TimeSlot.SLOT_08_10)
      expect(allSlots).toContain(TimeSlot.SLOT_22_24)
    })
  })

  describe('getTimeSlotDisplayName', () => {
    test('应该返回正确的显示名称', () => {
      expect(TimeSlotUtils.getTimeSlotDisplayName(TimeSlot.SLOT_08_10)).toBe('早上8-10点')
      expect(TimeSlotUtils.getTimeSlotDisplayName(TimeSlot.SLOT_12_14)).toBe('中午12-14点')
      expect(TimeSlotUtils.getTimeSlotDisplayName(TimeSlot.SLOT_18_20)).toBe('晚上18-20点')
      expect(TimeSlotUtils.getTimeSlotDisplayName(TimeSlot.SLOT_22_24)).toBe('深夜22-24点')
    })
  })

  describe('getTimeSlotHours', () => {
    test('应该返回正确的开始和结束小时', () => {
      expect(TimeSlotUtils.getTimeSlotHours(TimeSlot.SLOT_08_10)).toEqual({ start: 8, end: 10 })
      expect(TimeSlotUtils.getTimeSlotHours(TimeSlot.SLOT_14_16)).toEqual({ start: 14, end: 16 })
      expect(TimeSlotUtils.getTimeSlotHours(TimeSlot.SLOT_22_24)).toEqual({ start: 22, end: 24 })
    })
  })

  describe('isTimeInSlot', () => {
    test('应该正确判断时间是否在时段内', () => {
      const date1 = new Date('2024-01-01T08:30:00')
      expect(TimeSlotUtils.isTimeInSlot(date1, TimeSlot.SLOT_08_10)).toBe(true)
      expect(TimeSlotUtils.isTimeInSlot(date1, TimeSlot.SLOT_10_12)).toBe(false)

      const date2 = new Date('2024-01-01T09:59:59')
      expect(TimeSlotUtils.isTimeInSlot(date2, TimeSlot.SLOT_08_10)).toBe(true)

      const date3 = new Date('2024-01-01T10:00:00')
      expect(TimeSlotUtils.isTimeInSlot(date3, TimeSlot.SLOT_08_10)).toBe(false)
      expect(TimeSlotUtils.isTimeInSlot(date3, TimeSlot.SLOT_10_12)).toBe(true)
    })
  })
})

// 模拟测试数据
const mockCustomerProfiles = {
  retailOwner: `基本信息::店面类别: 实体店
基本信息::行业类目: 服装零售
基本信息::年营业额: 50万
抖音运营状态::是否抖音在做: 是`,

  officeWorker: `基本信息::职业: 上班族
基本信息::行业: IT互联网
基本信息::工作时间: 9-18点`,

  freelancer: `基本信息::职业: 自由职业者
基本信息::行业: 设计
基本信息::工作时间: 灵活`
}
describe('LLM预测测试', () => {
  test('LLM预测功能应该正确返回结果', async () => {
    const result = await TimeSlotPredictor.predictActiveTimeSlots('test_chat_id', mockCustomerProfiles.retailOwner)
  })
})

describe('客户活跃时段功能集成测试', () => {
  // 注意：这些测试需要数据库连接，在实际环境中运行

  test('应该能够初始化客户活跃时段数据', async () => {
    // 这个测试需要在有数据库连接的环境中运行
    // const chatId = 'test_chat_001'
    // const customerProfile = mockCustomerProfiles.retailOwner

    // await CustomerActivityService.initializeCustomer(chatId, customerProfile)

    // const stats = await CustomerActivityService.getActivityStats(chatId)
    // expect(stats).not.toBeNull()
    // expect(stats.totalSlots).toBe(8)

    console.log('集成测试需要数据库连接，跳过实际执行')
  })

  test('应该能够处理客户消息并更新权重', async () => {
    // const chatId = 'test_chat_002'
    // const messageTime = new Date('2024-01-01T18:30:00') // 晚上18:30

    // await CustomerActivityService.handleCustomerMessage(chatId, messageTime)

    // const bestSlots = await CustomerActivityService.getBestActiveTimeSlots(chatId, 3)
    // expect(bestSlots.length).toBeGreaterThan(0)

    console.log('集成测试需要数据库连接，跳过实际执行')
  })

  test('应该能够安排主动触达任务', async () => {
    // const chatId = 'test_chat_003'
    // const message = '同学，今天的学习怎么样？'

    // const result = await CustomerActivityService.scheduleProactiveMessage(chatId, message)
    // expect(result.success).toBe(true)
    // expect(result.scheduledTime).not.toBeNull()

    console.log('集成测试需要数据库连接，跳过实际执行')
  })
})

// 性能测试
describe('性能测试', () => {
  test('TimeSlotUtils方法应该快速执行', () => {
    const start = Date.now()

    // 执行1000次时段计算
    for (let i = 0; i < 1000; i++) {
      TimeSlotUtils.getTimeSlotByHour(Math.floor(Math.random() * 24))
      TimeSlotUtils.getAllTimeSlots()
      TimeSlotUtils.getTimeSlotDisplayName(TimeSlot.SLOT_18_20)
    }

    const end = Date.now()
    const duration = end - start

    expect(duration).toBeLessThan(100) // 应该在100ms内完成
  })
})

// 边界条件测试
describe('边界条件测试', () => {
  test('应该正确处理边界时间', () => {
    // 测试时段边界
    expect(TimeSlotUtils.getTimeSlotByHour(7.99)).toBeNull()
    expect(TimeSlotUtils.getTimeSlotByHour(8)).toBe(TimeSlot.SLOT_08_10)
    expect(TimeSlotUtils.getTimeSlotByHour(9.99)).toBe(TimeSlot.SLOT_08_10)
    expect(TimeSlotUtils.getTimeSlotByHour(10)).toBe(TimeSlot.SLOT_10_12)

    // 测试最后一个时段
    expect(TimeSlotUtils.getTimeSlotByHour(23.99)).toBe(TimeSlot.SLOT_22_24)
    expect(TimeSlotUtils.getTimeSlotByHour(24)).toBeNull()
  })

  test('应该处理无效输入', () => {
    expect(TimeSlotUtils.getTimeSlotByHour(-1)).toBeNull()
    expect(TimeSlotUtils.getTimeSlotByHour(NaN)).toBeNull()
    expect(TimeSlotUtils.getTimeSlotByHour(Infinity)).toBeNull()
  })
})

// 数据一致性测试
describe('数据一致性测试', () => {
  test('所有时段应该覆盖8-24点且无重叠', () => {
    const allSlots = TimeSlotUtils.getAllTimeSlots()
    const allHours = new Set<number>()

    for (const slot of allSlots) {
      const { start, end } = TimeSlotUtils.getTimeSlotHours(slot)

      // 检查时段范围是否合理
      expect(start).toBeGreaterThanOrEqual(8)
      expect(end).toBeLessThanOrEqual(24)
      expect(start).toBeLessThan(end)

      // 检查是否有重叠
      for (let hour = start; hour < end; hour++) {
        expect(allHours.has(hour)).toBe(false)
        allHours.add(hour)
      }
    }

    // 检查是否覆盖了8-24点的所有小时
    for (let hour = 8; hour < 24; hour++) {
      expect(allHours.has(hour)).toBe(true)
    }
  })

  test('时段枚举值应该与工具函数一致', () => {
    const allSlots = TimeSlotUtils.getAllTimeSlots()
    const enumValues = Object.values(TimeSlot)

    expect(allSlots.sort()).toEqual(enumValues.sort())
  })
})
