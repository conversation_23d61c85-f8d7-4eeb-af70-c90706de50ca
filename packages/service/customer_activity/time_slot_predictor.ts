import { LLM } from 'lib/ai/llm/llm_model'
import { XMLHelper } from 'lib/xml/xml'
import { TimeSlot } from 'model/typegoose/models/customer_activity'
import { TimeSlotUtils } from './time_slot_utils'
import logger from 'model/logger/logger'

/**
 * 预测结果接口
 */
export interface IPredictedTimeSlot {
  slot: TimeSlot
  weight: number
  reason: string
}

/**
 * 客户活跃时段预测器
 */
export class TimeSlotPredictor {
  /**
   * 根据客户画像预测活跃时段
   * @param chatId 聊天ID
   * @param customerProfile 客户画像信息
   * @returns 预测的时段权重数组
   */
  public static async predictActiveTimeSlots(
    chatId: string,
    customerProfile: string
  ): Promise<IPredictedTimeSlot[]> {
    try {
      const prompt = this.buildPredictionPrompt(customerProfile)

      const llm = new LLM({
        temperature: 0.3,
        max_tokens: 1000,
        meta: {
          chat_id: chatId,
          promptName: 'predict_active_time_slots',
          description: '预测客户活跃时段'
        }
      })

      const response = await llm.predict(prompt)
      return this.parsePredictionResponse(response)
    } catch (error) {
      logger.error(`预测客户活跃时段失败 for ${chatId}:`, error)
      return this.getDefaultTimeSlots()
    }
  }

  /**
   * 构建预测prompt
   */
  private static buildPredictionPrompt(customerProfile: string): string {
    const timeSlotDescriptions = TimeSlotUtils.getAllTimeSlots()
      .map((slot) => `- ${slot}: ${TimeSlotUtils.getTimeSlotDisplayName(slot)}`)
      .join('\n')

    return `你是一位专业的客户行为分析师，需要根据客户画像预测客户可能的活跃时段。

## 客户画像信息
${customerProfile}

## 可选时段
${timeSlotDescriptions}

## 分析要求
1. 根据客户的职业、行业、生活习惯等信息，分析其可能的活跃时段
2. 考虑不同职业的工作时间特点：
   - 上班族：通勤时间、午休时间、下班后时间较活跃
   - 实体店老板：营业时间相对较忙，早晚时间较活跃
   - 餐饮行业：中午晚上饭点更忙
   - 自由职业者：时间相对灵活
   - 学生：课余时间、晚上时间较活跃
3. 为每个预测的时段分配权重（0.1-1.0），权重越高表示越可能活跃
4. 最多选择3-4个时段，避免过于分散

## 输出格式
请严格按照以下XML格式输出：

<prediction>
<time_slot>
<slot>08-10</slot>
<weight>0.7</weight>
<reason>上班族通勤时间，可能在地铁上查看手机</reason>
</time_slot>
<time_slot>
<slot>18-20</slot>
<weight>0.9</weight>
<reason>下班时间，比较放松，有时间处理个人事务</reason>
</time_slot>
</prediction>

请开始分析并输出预测结果：`
  }

  /**
   * 解析预测响应
   */
  private static parsePredictionResponse(response: string): IPredictedTimeSlot[] {
    try {
      const predictionContent = XMLHelper.extractContent(response, 'prediction')
      if (!predictionContent) {
        logger.warn('无法提取prediction内容')
        return this.getDefaultTimeSlots()
      }

      const timeSlotMatches = predictionContent.match(/<time_slot>[\s\S]*?<\/time_slot>/g)
      if (!timeSlotMatches) {
        logger.warn('无法提取time_slot内容')
        return this.getDefaultTimeSlots()
      }

      const results: IPredictedTimeSlot[] = []

      for (const match of timeSlotMatches) {
        const slot = XMLHelper.extractContent(match, 'slot')
        const weightStr = XMLHelper.extractContent(match, 'weight')
        const reason = XMLHelper.extractContent(match, 'reason')

        if (slot && weightStr && reason) {
          const weight = parseFloat(weightStr)
          if (this.isValidTimeSlot(slot) && weight >= 0.1 && weight <= 1.0) {
            results.push({
              slot: slot as TimeSlot,
              weight: Math.min(Math.max(weight, 0.1), 1.0), // 确保权重在合理范围内
              reason: reason.trim()
            })
          }
        }
      }

      if (results.length === 0) {
        logger.warn('解析预测结果为空，使用默认时段')
        return this.getDefaultTimeSlots()
      }

      // 按权重排序
      results.sort((a, b) => b.weight - a.weight)

      // 最多返回4个时段
      return results.slice(0, 4)
    } catch (error) {
      logger.error('解析预测响应失败:', error)
      return this.getDefaultTimeSlots()
    }
  }

  /**
   * 验证时段是否有效
   */
  private static isValidTimeSlot(slot: string): boolean {
    return Object.values(TimeSlot).includes(slot as TimeSlot)
  }

  /**
   * 获取默认时段（当预测失败时使用）
   */
  private static getDefaultTimeSlots(): IPredictedTimeSlot[] {
    return [
      {
        slot: TimeSlot.SLOT_18_20,
        weight: 0.8,
        reason: '晚上时间，大多数人相对空闲'
      },
      {
        slot: TimeSlot.SLOT_20_22,
        weight: 0.7,
        reason: '晚上黄金时间，活跃度较高'
      },
      {
        slot: TimeSlot.SLOT_12_14,
        weight: 0.6,
        reason: '午休时间，可能查看手机'
      }
    ]
  }
}
