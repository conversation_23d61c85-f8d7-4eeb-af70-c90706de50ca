/**
 * ActivityTracker 功能测试
 *
 * 运行测试: npm test activity_tracker.test.ts
 */

import { ActivityTracker } from './activity_tracker'
import { ActivityTimeManager } from './activity_time_manager'
import { TimeSlotUtils } from './time_slot_utils'
import { TimeSlot, CustomerActivity } from 'model/typegoose/models/customer_activity'
import { getMongoConnection, closeMongoConnections } from 'model/typegoose/connection/mongo_connection'
import { getModelForClass } from '@typegoose/typegoose'

describe('ActivityTracker Tests', () => {
  let customerActivityModel: any
  const testChatId = 'test_chat_activity_tracker'
  const testCustomerProfile = `基本信息::店面类别: 实体店
基本信息::行业类目: 服装零售
基本信息::年营业额: 50万
抖音运营状态::是否抖音在做: 是`

  beforeAll(async () => {
    // 建立数据库连接
    const connection = await getMongoConnection('test_customer_activity')
    customerActivityModel = getModelForClass(CustomerActivity, { existingConnection: connection })

    console.log('数据库连接已建立')
  }, 30000)

  afterAll(async () => {
    // 清理测试数据
    try {
      await customerActivityModel.deleteMany({ chat_id: { $regex: /^test_/ } })
      console.log('测试数据已清理')
    } catch (error) {
      console.warn('清理测试数据失败:', error)
    }

    // 关闭数据库连接
    await closeMongoConnections()
    console.log('数据库连接已关闭')
  }, 10000)

  beforeEach(async () => {
    // 每个测试前清理特定的测试数据
    await customerActivityModel.deleteOne({ chat_id: testChatId })
  })

  describe('handleCustomerMessage', () => {
    test('应该能够处理客户消息并增加当前时段权重', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 模拟在18:30发送消息（晚上18-20点时段）
      const messageTime = new Date('2024-01-01T18:30:00')

      // 获取初始权重
      const initialActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const initialSlot = initialActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const initialWeight = initialSlot.weight

      // 处理客户消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)

      // 验证权重是否增加
      const updatedActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const updatedSlot = updatedActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)

      expect(updatedSlot.weight).toBeGreaterThan(initialWeight)
      expect(updatedSlot.last_updated).toBeInstanceOf(Date)

      console.log(`权重从 ${initialWeight} 增加到 ${updatedSlot.weight}`)
    }, 15000)

    test('应该忽略非活跃时段的消息', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 模拟在凌晨2:30发送消息（非活跃时段）
      const messageTime = new Date('2024-01-01T02:30:00')

      // 获取初始状态
      const initialActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const initialUpdateTime = initialActivity.updated_at

      // 处理客户消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)

      // 验证没有变化
      const updatedActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      expect(updatedActivity.updated_at.getTime()).toBe(initialUpdateTime.getTime())

      console.log('非活跃时段消息被正确忽略')
    }, 15000)

    test('应该处理不存在的客户数据', async () => {
      const nonExistentChatId = 'non_existent_chat'
      const messageTime = new Date('2024-01-01T18:30:00')

      // 这应该不会抛出错误
      await expect(ActivityTracker.handleCustomerMessage(nonExistentChatId, messageTime))
        .resolves.not.toThrow()

      console.log('不存在的客户数据处理正常')
    }, 10000)
  })

  describe('handleNoReply', () => {
    test('应该能够处理未回复情况并减少时段权重', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 模拟在12:30发送消息但未回复（中午12-14点时段）
      const sentTime = new Date('2024-01-01T12:30:00')

      // 获取初始权重
      const initialActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const initialSlot = initialActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_12_14)
      const initialWeight = initialSlot.weight

      // 处理未回复
      await ActivityTracker.handleNoReply(testChatId, sentTime)

      // 验证权重是否减少
      const updatedActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const updatedSlot = updatedActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_12_14)

      expect(updatedSlot.weight).toBeLessThan(initialWeight)
      expect(updatedSlot.last_updated).toBeInstanceOf(Date)

      console.log(`权重从 ${initialWeight} 减少到 ${updatedSlot.weight}`)
    }, 15000)

    test('应该忽略非活跃时段的未回复', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 模拟在凌晨3:30发送消息但未回复（非活跃时段）
      const sentTime = new Date('2024-01-01T03:30:00')

      // 获取初始状态
      const initialActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const initialUpdateTime = initialActivity.updated_at

      // 处理未回复
      await ActivityTracker.handleNoReply(testChatId, sentTime)

      // 验证没有变化
      const updatedActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      expect(updatedActivity.updated_at.getTime()).toBe(initialUpdateTime.getTime())

      console.log('非活跃时段未回复被正确忽略')
    }, 15000)
  })

  describe('ensureCustomerActivityExists', () => {
    test('应该为新客户初始化活跃时段数据', async () => {
      const newChatId = 'test_new_customer'

      // 确保客户不存在
      await customerActivityModel.deleteOne({ chat_id: newChatId })

      // 调用确保存在方法
      await ActivityTracker.ensureCustomerActivityExists(newChatId, testCustomerProfile)

      // 验证数据已创建
      const activity = await customerActivityModel.findOne({ chat_id: newChatId })
      expect(activity).toBeTruthy()
      expect(activity.time_slots).toHaveLength(8)

      // 清理
      await customerActivityModel.deleteOne({ chat_id: newChatId })

      console.log('新客户活跃时段数据初始化成功')
    }, 15000)

    test('应该跳过已存在的客户', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      const initialActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const initialCreatedAt = initialActivity.created_at

      // 再次调用确保存在方法
      await ActivityTracker.ensureCustomerActivityExists(testChatId, testCustomerProfile)

      // 验证没有重复创建
      const activity = await customerActivityModel.findOne({ chat_id: testChatId })
      expect(activity.created_at.getTime()).toBe(initialCreatedAt.getTime())

      console.log('已存在的客户数据未被重复创建')
    }, 15000)
  })

  describe('getNextBestContactTime', () => {
    test('应该返回最佳联系时间', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 获取最佳联系时间
      const bestTime = await ActivityTracker.getNextBestContactTime(testChatId)

      expect(bestTime).toBeTruthy()
      expect(bestTime!.slot).toBeDefined()
      expect(bestTime!.weight).toBeGreaterThan(0)
      expect(bestTime!.displayName).toBeDefined()
      expect(bestTime!.nextOccurrence).toBeInstanceOf(Date)

      console.log('最佳联系时间:', bestTime)
    }, 15000)

    test('应该为不存在的客户返回null', async () => {
      const nonExistentChatId = 'non_existent_chat_2'

      const bestTime = await ActivityTracker.getNextBestContactTime(nonExistentChatId)
      expect(bestTime).toBeNull()

      console.log('不存在的客户正确返回null')
    }, 10000)
  })

  describe('getActivityStats', () => {
    test('应该返回正确的统计信息', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 模拟一些活动来改变权重
      await ActivityTracker.handleCustomerMessage(testChatId, new Date('2024-01-01T18:30:00'))
      await ActivityTracker.handleCustomerMessage(testChatId, new Date('2024-01-01T20:30:00'))

      // 获取统计信息
      const stats = await ActivityTracker.getActivityStats(testChatId)

      expect(stats).toBeTruthy()
      expect(stats!.totalSlots).toBe(8)
      expect(stats!.predictedSlots).toBeGreaterThanOrEqual(0)
      expect(stats!.highWeightSlots).toBeGreaterThanOrEqual(0)
      expect(stats!.averageWeight).toBeGreaterThan(0)
      expect(stats!.lastUpdated).toBeInstanceOf(Date)

      console.log('活跃时段统计信息:', stats)
    }, 15000)

    test('应该为不存在的客户返回null', async () => {
      const nonExistentChatId = 'non_existent_chat_3'

      const stats = await ActivityTracker.getActivityStats(nonExistentChatId)
      expect(stats).toBeNull()

      console.log('不存在的客户统计信息正确返回null')
    }, 10000)
  })

  describe('边界条件测试', () => {
    test('应该处理边界时间', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      // 测试时段边界时间
      const boundaryTimes = [
        new Date('2024-01-01T08:00:00'), // 第一个时段开始
        new Date('2024-01-01T09:59:59'), // 第一个时段结束前
        new Date('2024-01-01T10:00:00'), // 第二个时段开始
        new Date('2024-01-01T23:59:59'), // 最后一个时段结束前
      ]

      for (const time of boundaryTimes) {
        await ActivityTracker.handleCustomerMessage(testChatId, time)
        const timeSlot = TimeSlotUtils.getTimeSlotByDate(time)
        console.log(`时间 ${time.toISOString()} 对应时段: ${timeSlot}`)
      }

      console.log('边界时间处理正常')
    }, 15000)

    test('应该处理权重边界值', async () => {
      // 先初始化客户活跃时段数据，设置特殊的权重配置
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        increaseAmount: 0.9, // 大幅增加
        decreaseAmount: 0.9, // 大幅减少
        minWeight: 0.1,
        maxWeight: 1.0
      })

      const messageTime = new Date('2024-01-01T18:30:00')

      // 多次增加权重，测试最大值限制
      for (let i = 0; i < 5; i++) {
        await ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      }

      const activity = await customerActivityModel.findOne({ chat_id: testChatId })
      const slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)

      expect(slot.weight).toBeLessThanOrEqual(1.0)
      console.log(`权重被正确限制在最大值: ${slot.weight}`)

      // 多次减少权重，测试最小值限制
      for (let i = 0; i < 10; i++) {
        await ActivityTracker.handleNoReply(testChatId, messageTime)
      }

      const updatedActivity = await customerActivityModel.findOne({ chat_id: testChatId })
      const updatedSlot = updatedActivity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)

      expect(updatedSlot.weight).toBeGreaterThanOrEqual(0.1)
      console.log(`权重被正确限制在最小值: ${updatedSlot.weight}`)
    }, 20000)
  })

  describe('并发测试', () => {
    test('应该处理并发的权重更新', async () => {
      // 先初始化客户活跃时段数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile)

      const messageTime = new Date('2024-01-01T18:30:00')

      // 并发处理多个消息
      const promises = Array.from({ length: 5 }, () =>
        ActivityTracker.handleCustomerMessage(testChatId, messageTime)
      )

      await Promise.all(promises)

      const activity = await customerActivityModel.findOne({ chat_id: testChatId })
      const slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)

      expect(slot.weight).toBeGreaterThan(0.5) // 应该有显著增加
      console.log(`并发处理后权重: ${slot.weight}`)
    }, 15000)
  })

  describe('冷却功能测试', () => {
    test('应该在冷却时间内阻止权重更新', async () => {
      // 初始化客户数据，设置短冷却时间便于测试
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        increaseAmount: 0.2,
        decreaseAmount: 0.1,
        minWeight: 0.1,
        maxWeight: 1.0,
        coolDown: 2000  // 2秒冷却时间
      })

      const messageTime = new Date('2024-01-01T18:30:00')

      // 第一次发送消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)

      // 获取第一次更新后的权重
      let activity = await customerActivityModel.findOne({ chat_id: testChatId })
      let slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const firstWeight = slot.weight

      // 立即发送第二条消息（应该被冷却机制阻止）
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)

      // 检查权重是否没有变化
      activity = await customerActivityModel.findOne({ chat_id: testChatId })
      slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const secondWeight = slot.weight

      expect(secondWeight).toBe(firstWeight)
      console.log(`冷却机制生效，权重保持: ${secondWeight}`)
    }, 15000)

    test('应该在冷却时间结束后允许权重更新', async () => {
      // 初始化客户数据，设置很短的冷却时间
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        increaseAmount: 0.2,
        coolDown: 100  // 100毫秒冷却时间
      })

      const messageTime = new Date('2024-01-01T18:30:00')

      // 第一次发送消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)

      // 获取第一次更新后的权重
      let activity = await customerActivityModel.findOne({ chat_id: testChatId })
      let slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const firstWeight = slot.weight

      // 等待冷却时间结束
      await new Promise((resolve) => setTimeout(resolve, 200))

      // 再次发送消息（应该成功更新）
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime)

      // 检查权重是否增加
      activity = await customerActivityModel.findOne({ chat_id: testChatId })
      slot = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const secondWeight = slot.weight

      expect(secondWeight).toBeGreaterThan(firstWeight)
      console.log(`冷却时间结束，权重从 ${firstWeight} 增加到 ${secondWeight}`)
    }, 15000)

    test('不同时段的冷却应该独立', async () => {
      // 初始化客户数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        coolDown: 5000  // 5秒冷却时间
      })

      const messageTime1 = new Date('2024-01-01T18:30:00') // 18-20时段
      const messageTime2 = new Date('2024-01-01T20:30:00') // 20-22时段

      // 在第一个时段发送消息
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime1)

      // 立即在第二个时段发送消息（不应该被冷却影响）
      await ActivityTracker.handleCustomerMessage(testChatId, messageTime2)

      const activity = await customerActivityModel.findOne({ chat_id: testChatId })
      const slot1 = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_18_20)
      const slot2 = activity.time_slots.find((ts: any) => ts.slot === TimeSlot.SLOT_20_22)

      // 两个时段的权重都应该有更新
      expect(slot1.weight).toBeGreaterThan(0.3)
      expect(slot2.weight).toBeGreaterThan(0.3)

      console.log(`时段1权重: ${slot1.weight}, 时段2权重: ${slot2.weight}`)
    }, 15000)

    test('应该支持更新冷却配置', async () => {
      // 初始化客户数据
      await ActivityTimeManager.initializeCustomerActivity(testChatId, testCustomerProfile, {
        coolDown: 1000  // 1秒冷却时间
      })

      // 更新冷却配置
      await ActivityTimeManager.updateWeightConfig(testChatId, {
        coolDown: 100  // 改为100毫秒
      })

      // 验证配置已更新
      const activity = await customerActivityModel.findOne({ chat_id: testChatId })
      expect(activity.cooldown_ms).toBe(100)

      console.log(`冷却配置已更新为: ${activity.cooldown_ms}ms`)
    }, 15000)
  })
})
