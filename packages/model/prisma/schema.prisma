generator client {
  provider = "prisma-client-js"
  output   = "../prisma_client"
}

datasource db {
  provider = "mongodb"
  url      = "mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/yuhe?authSource=admin"
}

type ChatContact {
  wx_id   String
  wx_name String
}

type ChatState {
  nodeInvokeCount Json
  nextStage       String
  userSlots       Json
  state           Json
}

model chat {
  id                 String      @id @map("_id")
  contact            ChatContact
  round_ids          String[]
  wx_id              String
  is_human_involved  Boolean?
  created_at         DateTime?   @db.Date
  chat_state         ChatState
  course_no          Int?
  course_no_ori      Int?
  pay_time           DateTime?
  is_deleted         Boolean?
  is_stop_group_push Boolean? // 停止群发
  phone              String? // 手机号
  ip                 String? // 品牌渠道

  @@index([phone])
  @@index([wx_id, course_no])
  @@index([course_no])
  @@index([contact.wx_name])
  @@index([created_at])
}

model chat_history {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id    String
  content    String
  created_at DateTime @db.Date
  role       String

  is_send_by_human  Boolean? // 是否人工回复
  short_description String? // SOP 描述
  round_id          String? // LLM 输出，会绑定 round_id
  is_recalled       Boolean? // 撤回
  message_id        String?    @unique // 消息 ID
  chat_state        ChatState? // 产生回复时的 chat_state
  sop_id            String?
  state             String?

  @@index([chat_id, created_at], map: "chat_id_1_created_at_1")
  @@index([role])
}

model account {
  id     String  @id @map("_id")
  name   String
  avatar String?
}

model log_store {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id   String?
  level     String
  timestamp DateTime @db.Date
  msg       String

  @@index([chat_id, timestamp])
}

model event_track {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id   String
  type      String
  meta      Json?
  timestamp DateTime @db.Date

  // @@index([type, meta.course_no, timestamp]) prisma 无法新建，需要手动添加一下
  @@index([type])
  @@index([type, timestamp], map: "type_1_timestamp_1")
}

model rag_map_oss {
  id                String @id @default(auto()) @map("_id") @db.ObjectId
  rag_resource_name String @unique
  oss_url           String
}

model rag_supplement_questions {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id      String?
  timestamp    DateTime @db.Date
  scheduleTime Json
  database     String
  msg          String
}

model danmu {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId
  content  String // 弹幕的内容
  time     Int // 弹幕出现的时间，单位是秒或毫秒
  liveId   String // 直播间的 ID
  sendTime DateTime? @db.Date // 发送弹幕的时间
  courseNo Int? // 期数
  day      Int? // 第几天的弹幕

  userId   String // 发送弹幕的客户ID
  userName String // 发送弹幕的客户名

  @@index([userId, liveId, time], map: "userId_1_liveId_1_time_1")
}

model config {
  id             String  @id @default(auto()) @map("_id") @db.ObjectId
  enterpriseName String
  accountName    String
  nickName       String?

  wechatId         String
  address          String
  port             String
  botUserId        String
  orgToken         String
  enterpriseConfig Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([wechatId])
  @@index([accountName])
  @@index([enterpriseName])
}

model sop_record {
  id                String @id @default(auto()) @map("_id") @db.ObjectId
  course_no         Int
  sop_id            String
  sent_count        Int
  reply_count_30min Int
  reply_count_60min Int
}

model message_annotation {
  id              String     @id @default(auto()) @map("_id") @db.ObjectId
  chat_id         String
  user_name       String?
  course_no       Int?
  chat_history_id String
  usr_message     String
  ai_message      String
  think           String?
  strategy        String?
  round_id        String?
  chat_state      ChatState?
  title           String?
  description     String?
  tags            String[]
  type            String
  created_at      DateTime   @db.Date

  @@index([chat_id])
}

model sop {
  id         String      @id @default(auto()) @map("_id") @db.ObjectId
  title      String
  week       Int
  day        Int
  time       String
  situations Situation[]
  enable     Boolean
  tag        String
  topic      String
}

// 客户活跃时段相关类型定义
type TimeSlotWeight {
  slot         String   // 时段枚举值 (08-10, 10-12, 12-14, 14-16, 16-18, 18-20, 20-22, 22-24)
  weight       Float    // 权重值 (0.1-1.0)
  is_predicted Boolean  // 是否为AI预测的时段
  last_updated DateTime // 最后更新时间
}

// 客户活跃时段数据模型
model customer_activity {
  id                     String            @id @default(auto()) @map("_id") @db.ObjectId
  chat_id                String            @unique // 聊天ID
  time_slots             TimeSlotWeight[]  // 时段权重数组
  weight_increase_factor Float             @default(0.2) // 权重增加系数
  weight_decrease_factor Float             @default(0.1) // 权重减少系数
  min_weight             Float             @default(0.1) // 最小权重
  max_weight             Float             @default(1.0) // 最大权重
  cooldown_ms            Int               @default(300000) // 冷却时间（毫秒），默认5分钟
  created_at             DateTime          @default(now()) @db.Date
  updated_at             DateTime          @updatedAt @db.Date

  @@index([chat_id])
  @@index([updated_at])
}

model sop_tag {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  enable         Boolean
  enable_account String[]
}

model sop_topic {
  id         String           @id @default(auto()) @map("_id") @db.ObjectId
  name       String
  enable     Boolean
  tag        String
  conditions TopicCondition[]
}

type TopicCondition {
  isOrNotIs Boolean
  condition String
}

model group {
  id               String @id @default(auto()) @map("_id") @db.ObjectId
  owner_account_id String
  group_id         String @unique
  course_no        Int
  name             String
}

model group_sop {
  id         String           @id @default(auto()) @map("_id") @db.ObjectId
  title      String
  week       Int
  day        Int
  time       String
  situations GroupSituation[]
  enable     Boolean
  tag        String
  topic      String
}

model group_sop_tag {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  enable       Boolean
  enable_group String[]
}

model group_sop_topic {
  id     String  @id @default(auto()) @map("_id") @db.ObjectId
  name   String
  enable Boolean
  tag    String
}

model group_teacher {
  id              String @id @default(auto()) @map("_id") @db.ObjectId
  accountWechatId String @unique
  imContactId     String
  name            String
  wecomUserId     String
}

type Situation {
  conditions Condition[]
  action     Json[]
}

type GroupSituation {
  conditions GroupCondition[]
  action     Json[]
}

type Condition {
  //逻辑是正还是负
  isOrNotIs Boolean
  type      String
  condition String
}

type GroupCondition {
  isOrNotIs Boolean
  condition String
}

model dashboard_tag {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  name       String   @unique
  color      String? // 标签颜色，可选
  created_at DateTime @default(now()) @db.Date
  updated_at DateTime @updatedAt @db.Date
}

model dashboard_data {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id         String
  chat_history_id String[]
  tag_ids         String[] // 改为存储标签ID数组
  description     String
  created_at      DateTime @default(now()) @db.Date
}

model ip_table {
  id              String @id @default(auto()) @map("_id") @db.ObjectId
  start_course_no Int
  end_course_no   Int
  account         String
  ip              String
}
