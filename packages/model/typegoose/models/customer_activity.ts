import { prop, modelOptions, index } from '@typegoose/typegoose'
import { Types } from 'mongoose'

/**
 * 时段枚举 - 8-24点分成8个时段，每2小时一个bin
 */
export enum TimeSlot {
  SLOT_08_10 = '08-10',  // 8:00-10:00
  SLOT_10_12 = '10-12',  // 10:00-12:00
  SLOT_12_14 = '12-14',  // 12:00-14:00
  SLOT_14_16 = '14-16',  // 14:00-16:00
  SLOT_16_18 = '16-18',  // 16:00-18:00
  SLOT_18_20 = '18-20',  // 18:00-20:00
  SLOT_20_22 = '20-22',  // 20:00-22:00
  SLOT_22_24 = '22-24'   // 22:00-24:00
}

/**
 * 时段权重信息
 */
export class TimeSlotWeight {
  @prop({ required: true, enum: TimeSlot })
  public slot!: TimeSlot

  @prop({ required: true, default: 0.5 })
  public weight!: number

  @prop({ required: true, default: false })
  public is_predicted!: boolean  // 是否为AI预测的时段

  @prop({ type: Date, default: Date.now })
  public last_updated!: Date
}

/**
 * 客户活跃时段数据模型
 */
@modelOptions({
  schemaOptions: {
    collection: 'customer_activity',
    timestamps: false
  }
})
@index({ chat_id: 1 }, { unique: true })
export class CustomerActivity {
  @prop({ type: () => Types.ObjectId, default: () => new Types.ObjectId() })
  public _id!: Types.ObjectId

  @prop({ required: true })
  public chat_id!: string

  @prop({ type: () => [TimeSlotWeight], default: [] })
  public time_slots!: TimeSlotWeight[]

  @prop({ type: Date, default: Date.now })
  public created_at!: Date

  @prop({ type: Date, default: Date.now })
  public updated_at!: Date

  @prop({ default: 1.0 })
  public weight_increase_factor!: number  // 权重增加系数

  @prop({ default: 0.8 })
  public weight_decrease_factor!: number  // 权重减少系数

  @prop({ default: 0.1 })
  public min_weight!: number  // 最小权重

  @prop({ default: 1.0 })
  public max_weight!: number  // 最大权重

  @prop({ default: 300000 })  // 默认5分钟 = 5 * 60 * 1000ms
  public cooldown_ms!: number  // 冷却时间（毫秒）
}
